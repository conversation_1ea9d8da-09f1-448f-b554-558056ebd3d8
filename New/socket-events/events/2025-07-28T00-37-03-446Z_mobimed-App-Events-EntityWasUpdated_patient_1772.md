# mobimed:App\Events\EntityWasUpdated

## Event Information

- **Event Type**: mobimed:App\Events\EntityWasUpdated
- **Model**: patient
- **Event ID**: 1772
- **Timestamp**: 2025-07-28T00:37:03.446Z
- **Webhook Event**: EntityWasUpdated
- **Webhook Model**: Patient

## Original Socket Event Data

```json
{
  "type": "patient",
  "payload": {
    "id": 1772,
    "createdAt": "2025-07-27T22:59:25.000Z",
    "updatedAt": "2025-07-28T00:28:38.000Z",
    "createdBy": 5003,
    "updatedBy": 5003,
    "firstName": "WH",
    "lastName": "Test",
    "dob": null,
    "ssn": null,
    "flashMessage": "",
    "active": true,
    "phoneMobile": null,
    "phonePersonal": null,
    "phoneBusiness": null,
    "email": null,
    "title": null,
    "titleSuffix": null,
    "healthInsurance": null,
    "gender": null,
    "addresses": [
      {
        "id": 1766,
        "label": null,
        "name": null,
        "street": null,
        "streetNumber": null,
        "postalCode": null,
        "city": null,
        "country": "US",
        "primary": 1
      }
    ],
    "categories": [],
    "customFields": [
      11507,
      11508,
      11509,
      11510,
      11511,
      11512,
      11513,
      11514,
      11515,
      11516,
      11517
    ],
    "invoices": [],
    "payments": [],
    "files": [],
    "history": [],
    "appointments": [],
    "messages": [],
    "medications": [],
    "personalWebForms": [],
    "qrUrl": "https://api-ccdemo.clinicore.eu/v1/qr/100/1772.png",
    "avatarUrl": null
  },
  "socket": null
}
```

## Converted Webhook Data

```json
{
  "event": "EntityWasUpdated",
  "model": "Patient",
  "id": 1772,
  "payload": {
    "id": 1772,
    "createdAt": "2025-07-27T22:59:25.000Z",
    "updatedAt": "2025-07-28T00:28:38.000Z",
    "createdBy": 5003,
    "updatedBy": 5003,
    "firstName": "WH",
    "lastName": "Test",
    "dob": null,
    "ssn": null,
    "flashMessage": "",
    "active": true,
    "phoneMobile": null,
    "phonePersonal": null,
    "phoneBusiness": null,
    "email": null,
    "title": null,
    "titleSuffix": null,
    "healthInsurance": null,
    "gender": null,
    "addresses": [
      {
        "id": 1766,
        "label": null,
        "name": null,
        "street": null,
        "streetNumber": null,
        "postalCode": null,
        "city": null,
        "country": "US",
        "primary": 1
      }
    ],
    "categories": [],
    "customFields": [
      11507,
      11508,
      11509,
      11510,
      11511,
      11512,
      11513,
      11514,
      11515,
      11516,
      11517
    ],
    "invoices": [],
    "payments": [],
    "files": [],
    "history": [],
    "appointments": [],
    "messages": [],
    "medications": [],
    "personalWebForms": [],
    "qrUrl": "https://api-ccdemo.clinicore.eu/v1/qr/100/1772.png",
    "avatarUrl": null
  },
  "timestamp": "2025-07-28T00:37:03.446Z"
}
```

## Event Processing

This event was received from the CC socket server and converted to webhook format for processing by the DermaCare sync system.

### Processing Flow
1. **Socket Event Received**: mobimed:App\Events\EntityWasUpdated
2. **Data Extraction**: Extracted model type and payload data
3. **Webhook Conversion**: Converted to webhook format compatible with ccHandler.ts
4. **Webhook Transmission**: Sent to dev server at http://localhost:8787/webhooks/cc

---
*Generated by DermaCare Socket Event Logger*
