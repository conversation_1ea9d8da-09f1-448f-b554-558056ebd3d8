# mobimed:App\Events\EntityWasUpdated

## Event Information

- **Event Type**: mobimed:App\Events\EntityWasUpdated
- **Model**: patient
- **Event ID**: 1771
- **Timestamp**: 2025-07-28T00:43:49.534Z
- **Webhook Event**: EntityWasUpdated
- **Webhook Model**: Patient

## Original Socket Event Data

```json
{
  "type": "patient",
  "payload": {
    "id": 1771,
    "createdAt": "2025-07-26T20:21:03.000Z",
    "updatedAt": "2025-07-28T00:43:48.000Z",
    "createdBy": 5003,
    "updatedBy": 5003,
    "firstName": "WB",
    "lastName": "Test",
    "dob": "1994-02-01T00:00:00.000Z",
    "ssn": null,
    "flashMessage": "",
    "active": true,
    "phoneMobile": "+880 1760-147852",
    "phonePersonal": null,
    "phoneBusiness": null,
    "email": "<EMAIL>",
    "title": null,
    "titleSuffix": null,
    "healthInsurance": null,
    "gender": null,
    "addresses": [],
    "categories": [],
    "customFields": [
      11467,
      11469,
      11518,
      11519,
      11520,
      11521,
      11522,
      11523,
      11524,
      11525,
      11526,
      11527,
      11528,
      11468,
      11470
    ],
    "invoices": [],
    "payments": [],
    "files": [],
    "history": [],
    "appointments": [],
    "messages": [],
    "medications": [],
    "personalWebForms": [],
    "qrUrl": "https://api-ccdemo.clinicore.eu/v1/qr/100/1771.png",
    "avatarUrl": null
  },
  "socket": null
}
```

## Converted Webhook Data

```json
{
  "event": "EntityWasUpdated",
  "model": "Patient",
  "id": 1771,
  "payload": {
    "id": 1771,
    "createdAt": "2025-07-26T20:21:03.000Z",
    "updatedAt": "2025-07-28T00:43:48.000Z",
    "createdBy": 5003,
    "updatedBy": 5003,
    "firstName": "WB",
    "lastName": "Test",
    "dob": "1994-02-01T00:00:00.000Z",
    "ssn": null,
    "flashMessage": "",
    "active": true,
    "phoneMobile": "+880 1760-147852",
    "phonePersonal": null,
    "phoneBusiness": null,
    "email": "<EMAIL>",
    "title": null,
    "titleSuffix": null,
    "healthInsurance": null,
    "gender": null,
    "addresses": [],
    "categories": [],
    "customFields": [
      11467,
      11469,
      11518,
      11519,
      11520,
      11521,
      11522,
      11523,
      11524,
      11525,
      11526,
      11527,
      11528,
      11468,
      11470
    ],
    "invoices": [],
    "payments": [],
    "files": [],
    "history": [],
    "appointments": [],
    "messages": [],
    "medications": [],
    "personalWebForms": [],
    "qrUrl": "https://api-ccdemo.clinicore.eu/v1/qr/100/1771.png",
    "avatarUrl": null
  },
  "timestamp": "2025-07-28T00:43:49.534Z"
}
```

## Event Processing

This event was received from the CC socket server and converted to webhook format for processing by the DermaCare sync system.

### Processing Flow
1. **Socket Event Received**: mobimed:App\Events\EntityWasUpdated
2. **Data Extraction**: Extracted model type and payload data
3. **Webhook Conversion**: Converted to webhook format compatible with ccHandler.ts
4. **Webhook Transmission**: Sent to dev server at http://localhost:8787/webhooks/cc

---
*Generated by DermaCare Socket Event Logger*
