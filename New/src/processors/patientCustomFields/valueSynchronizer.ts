/**
 * Patient Custom Fields Value Synchronizer
 *
 * Provides comprehensive bidirectional custom field value synchronization between
 * AutoPatient (AP) and CliniCore (CC) platforms. Implements the core synchronization
 * methods with proper error handling, webhook loop prevention, and comprehensive
 * logging for production-ready patient data synchronization.
 *
 * **Key Features:**
 * - Bidirectional value synchronization (AP ↔ CC)
 * - Webhook loop prevention with timestamp updates
 * - Comprehensive error handling and logging
 * - Field mapping resolution and value conversion
 * - Graceful handling of missing fields and mappings
 * - Request ID correlation for tracing
 *
 * @fileoverview Core patient custom field value synchronization engine
 * @version 1.0.0
 * @since 2024-07-27
 */

import { dbSchema, getDb } from "@database";
import { eq } from "drizzle-orm";
import { contactReq, patientReq } from "@/apiClient";
import { logDatabaseError } from "@/utils/errorLogger";
import { logDebug, logError, logInfo, logWarn } from "@/utils/logger";
import { convertFieldValue } from "./valueConverter";
import type {
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON>ientRecord,
	CustomFieldMapping,
	SyncOperationContext,
	ValueConversionContext,
	APCustomFieldValue,
	APCustomFieldUpdatePayload,
} from "./types";

/**
 * Synchronize custom field values from AutoPatient to CliniCore
 *
 * Retrieves custom field values from AutoPatient contact and synchronizes them
 * to the corresponding CliniCore patient. Follows the exact sequence specified
 * in requirements with proper error handling and webhook loop prevention.
 *
 * **Process Flow:**
 * 1. Input validation and patient record retrieval
 * 2. Custom field data retrieval from AP
 * 3. Field mapping resolution from database
 * 4. **CRITICAL**: Timestamp update before API calls (webhook loop prevention)
 * 5. Value conversion and API synchronization to CC
 * 6. Comprehensive logging and result reporting
 *
 * @param patientId - Local database patient ID (string, validated as non-empty)
 * @param requestId - Request ID for tracing and correlation
 * @param skipMissingFields - Whether to skip missing fields instead of failing
 * @returns Promise resolving to synchronization result with statistics and errors
 *
 * @example
 * ```typescript
 * const result = await syncApToCcCustomFields("patient-123", "req-456", true);
 * if (result.success) {
 *   console.log(`Synchronized ${result.fieldsProcessed} fields successfully`);
 * } else {
 *   console.error('Sync failed:', result.errors);
 * }
 * ```
 *
 * @since 1.0.0
 */
export async function syncApToCcCustomFields(
	patientId: string,
	requestId: string,
	skipMissingFields: boolean = false,
): Promise<SyncResult> {
	logInfo("Starting AP to CC custom field value synchronization", {
		requestId,
		patientId,
		skipMissingFields,
	});

	const result: SyncResult = {
		success: false,
		fieldsProcessed: 0,
		errors: [],
	};

	try {
		// Step 1: Input validation and patient record retrieval
		const patient = await validateAndGetPatient(patientId, requestId);
		if (!patient) {
			result.errors.push("Patient not found in database");
			return result;
		}

		if (!patient.apId) {
			result.errors.push("Patient does not have AutoPatient ID");
			return result;
		}

		if (!patient.ccId) {
			result.errors.push("Patient does not have CliniCore ID");
			return result;
		}

		logDebug("Patient validation successful", {
			requestId,
			patientId,
			apId: patient.apId,
			ccId: patient.ccId,
		});

		// Step 2: Custom field data retrieval from AP
		const apContact = await contactReq.get(patient.apId);
		if (!apContact) {
			result.errors.push("Failed to retrieve AutoPatient contact data");
			return result;
		}

		const apCustomFieldsRaw = apContact.customFields || [];
		// Transform AP custom fields to match our interface
		const apCustomFields: APCustomFieldValue[] = apCustomFieldsRaw.map(field => ({
			id: field.id,
			field_value: field.value as string | number,
		}));
		logDebug("Retrieved AP custom field values", {
			requestId,
			apId: patient.apId,
			customFieldCount: apCustomFields.length,
		});

		// Step 3: Field mapping resolution from database
		const fieldMappings = await getFieldMappings(requestId);
		logDebug("Retrieved field mappings", {
			requestId,
			mappingCount: fieldMappings.length,
		});

		// Step 4: CRITICAL - Update timestamp BEFORE API calls (webhook loop prevention)
		await updatePatientTimestamp(patientId, "cc", requestId);
		logInfo("Updated CC timestamp for webhook loop prevention", {
			requestId,
			patientId,
			timestamp: new Date().toISOString(),
		});

		// Step 5: Value conversion and API synchronization
		const context: SyncOperationContext = {
			patient,
			sourcePlatform: "ap",
			targetPlatform: "cc",
			requestId,
			skipMissingFields,
			isReturnCallback: false,
		};

		const syncResult = await performApToCcSync(apCustomFields, fieldMappings, context);
		result.fieldsProcessed = syncResult.fieldsProcessed;
		result.errors.push(...syncResult.errors);

		// Step 6: Final result determination
		result.success = result.errors.length === 0;

		logInfo("AP to CC synchronization completed", {
			requestId,
			patientId,
			success: result.success,
			fieldsProcessed: result.fieldsProcessed,
			errorCount: result.errors.length,
		});

		return result;
	} catch (error) {
		const errorMessage = `AP to CC synchronization failed: ${String(error)}`;
		logError(errorMessage, { requestId, patientId, error });
		await logDatabaseError(
			error instanceof Error ? error : new Error(String(error)),
			"custom_field_value_sync",
			{ patientId, direction: "ap_to_cc", requestId },
		);
		result.errors.push(errorMessage);
		return result;
	}
}

/**
 * Synchronize custom field values from CliniCore to AutoPatient
 *
 * Retrieves custom field values from CliniCore patient and synchronizes them
 * to the corresponding AutoPatient contact. Follows the exact sequence specified
 * in requirements with proper error handling and webhook loop prevention.
 *
 * **Process Flow:**
 * 1. Input validation and patient record retrieval
 * 2. Custom field data retrieval from CC (by field IDs)
 * 3. Field mapping resolution from database
 * 4. **CRITICAL**: Timestamp update before API calls (webhook loop prevention)
 * 5. Value conversion and API synchronization to AP
 * 6. Comprehensive logging and result reporting
 *
 * @param patientId - Local database patient ID (string, validated as non-empty)
 * @param requestId - Request ID for tracing and correlation
 * @param skipMissingFields - Whether to skip missing fields instead of failing
 * @returns Promise resolving to synchronization result with statistics and errors
 *
 * @example
 * ```typescript
 * const result = await syncCcToApCustomFields("patient-123", "req-456", true);
 * if (result.success) {
 *   console.log(`Synchronized ${result.fieldsProcessed} fields successfully`);
 * } else {
 *   console.error('Sync failed:', result.errors);
 * }
 * ```
 *
 * @since 1.0.0
 */
export async function syncCcToApCustomFields(
	patientId: string,
	requestId: string,
	skipMissingFields: boolean = false,
): Promise<SyncResult> {
	logInfo("Starting CC to AP custom field value synchronization", {
		requestId,
		patientId,
		skipMissingFields,
	});

	const result: SyncResult = {
		success: false,
		fieldsProcessed: 0,
		errors: [],
	};

	try {
		// Step 1: Input validation and patient record retrieval
		const patient = await validateAndGetPatient(patientId, requestId);
		if (!patient) {
			result.errors.push("Patient not found in database");
			return result;
		}

		if (!patient.ccId) {
			result.errors.push("Patient does not have CliniCore ID");
			return result;
		}

		if (!patient.apId) {
			result.errors.push("Patient does not have AutoPatient ID");
			return result;
		}

		logDebug("Patient validation successful", {
			requestId,
			patientId,
			ccId: patient.ccId,
			apId: patient.apId,
		});

		// Step 2: Custom field data retrieval from CC (by field IDs)
		const fieldMappings = await getFieldMappings(requestId);
		const ccFieldIds = fieldMappings
			.filter(mapping => mapping.ccId !== null)
			.map(mapping => mapping.ccId as number);

		if (ccFieldIds.length === 0) {
			logWarn("No CC field mappings found", { requestId, patientId });
			result.success = true; // Not an error, just no fields to sync
			return result;
		}

		const ccCustomFields = await patientReq.customFields(ccFieldIds);
		logDebug("Retrieved CC custom field values", {
			requestId,
			ccId: patient.ccId,
			customFieldCount: ccCustomFields.length,
			requestedFieldIds: ccFieldIds,
		});

		// Step 3: CRITICAL - Update timestamp BEFORE API calls (webhook loop prevention)
		await updatePatientTimestamp(patientId, "ap", requestId);
		logInfo("Updated AP timestamp for webhook loop prevention", {
			requestId,
			patientId,
			timestamp: new Date().toISOString(),
		});

		// Step 4: Value conversion and API synchronization
		const context: SyncOperationContext = {
			patient,
			sourcePlatform: "cc",
			targetPlatform: "ap",
			requestId,
			skipMissingFields,
			isReturnCallback: false,
		};

		const syncResult = await performCcToApSync(ccCustomFields, fieldMappings, context);
		result.fieldsProcessed = syncResult.fieldsProcessed;
		result.errors.push(...syncResult.errors);

		// Step 5: Final result determination
		result.success = result.errors.length === 0;

		logInfo("CC to AP synchronization completed", {
			requestId,
			patientId,
			success: result.success,
			fieldsProcessed: result.fieldsProcessed,
			errorCount: result.errors.length,
		});

		return result;
	} catch (error) {
		const errorMessage = `CC to AP synchronization failed: ${String(error)}`;
		logError(errorMessage, { requestId, patientId, error });
		await logDatabaseError(
			error instanceof Error ? error : new Error(String(error)),
			"custom_field_value_sync",
			{ patientId, direction: "cc_to_ap", requestId },
		);
		result.errors.push(errorMessage);
		return result;
	}
}

/**
 * Validate patient ID and retrieve patient record
 */
async function validateAndGetPatient(
	patientId: string,
	requestId: string,
): Promise<PatientRecord | null> {
	if (!patientId || patientId.trim().length === 0) {
		logError("Invalid patient ID provided", { requestId, patientId });
		return null;
	}

	try {
		const db = getDb();
		const patients = await db
			.select()
			.from(dbSchema.patient)
			.where(eq(dbSchema.patient.id, patientId.trim()));

		if (patients.length === 0) {
			logWarn("Patient not found in database", { requestId, patientId });
			return null;
		}

		return patients[0];
	} catch (error) {
		logError("Failed to retrieve patient from database", {
			requestId,
			patientId,
			error,
		});
		return null;
	}
}

/**
 * Get all custom field mappings from database
 */
async function getFieldMappings(requestId: string): Promise<CustomFieldMapping[]> {
	try {
		const db = getDb();
		const mappings = await db.select().from(dbSchema.customFields);

		logDebug("Retrieved field mappings from database", {
			requestId,
			mappingCount: mappings.length,
		});

		return mappings;
	} catch (error) {
		logError("Failed to retrieve field mappings", { requestId, error });
		throw error;
	}
}

/**
 * Update patient timestamp for webhook loop prevention
 */
async function updatePatientTimestamp(
	patientId: string,
	platform: "ap" | "cc",
	requestId: string,
): Promise<void> {
	try {
		const db = getDb();
		const now = new Date();

		const updateData = platform === "ap"
			? { apUpdatedAt: now, updatedAt: now }
			: { ccUpdatedAt: now, updatedAt: now };

		await db
			.update(dbSchema.patient)
			.set(updateData)
			.where(eq(dbSchema.patient.id, patientId));

		logDebug("Updated patient timestamp", {
			requestId,
			patientId,
			platform,
			timestamp: now.toISOString(),
		});
	} catch (error) {
		logError("Failed to update patient timestamp", {
			requestId,
			patientId,
			platform,
			error,
		});
		throw error;
	}
}

/**
 * Perform AP to CC synchronization
 */
async function performApToCcSync(
	apCustomFields: APCustomFieldValue[],
	fieldMappings: CustomFieldMapping[],
	context: SyncOperationContext,
): Promise<{ fieldsProcessed: number; errors: string[] }> {
	const { patient, requestId, skipMissingFields } = context;
	const errors: string[] = [];
	let fieldsProcessed = 0;

	// Build CC update payload
	const ccUpdatePayload: Record<number, string | number | boolean | null> = {};

	for (const apField of apCustomFields) {
		try {
			// Find mapping for this AP field
			const mapping = fieldMappings.find(m => m.apId === apField.id);
			if (!mapping || !mapping.ccId) {
				if (skipMissingFields) {
					logWarn("Skipping AP field without CC mapping", {
						requestId,
						apFieldId: apField.id,
						reason: "No mapping found",
					});
					continue;
				} else {
					errors.push(`No CC mapping found for AP field ${apField.id}`);
					continue;
				}
			}

			// Convert value
			const conversionContext: ValueConversionContext = {
				sourceField: mapping.apConfig!,
				targetField: mapping.ccConfig!,
				sourcePlatform: "ap",
				targetPlatform: "cc",
				requestId,
			};

			const conversionResult = await convertFieldValue(apField.field_value, conversionContext);
			if (!conversionResult.success) {
				if (skipMissingFields) {
					logWarn("Skipping AP field due to conversion failure", {
						requestId,
						apFieldId: apField.id,
						error: conversionResult.error,
					});
					continue;
				} else {
					errors.push(`Value conversion failed for AP field ${apField.id}: ${conversionResult.error}`);
					continue;
				}
			}

			// Add to CC update payload
			const convertedValue = conversionResult.convertedValue!;
			// Handle array values by converting to string
			ccUpdatePayload[mapping.ccId] = Array.isArray(convertedValue)
				? convertedValue.join(', ')
				: convertedValue as string | number | boolean | null;
			fieldsProcessed++;

			logDebug("Converted AP field value for CC", {
				requestId,
				apFieldId: apField.id,
				ccFieldId: mapping.ccId,
				originalValue: apField.field_value,
				convertedValue: conversionResult.convertedValue,
			});
		} catch (error) {
			const errorMessage = `Failed to process AP field ${apField.id}: ${String(error)}`;
			logError(errorMessage, { requestId, error });
			errors.push(errorMessage);
		}
	}

	// Update CC patient if we have fields to sync
	if (Object.keys(ccUpdatePayload).length > 0) {
		try {
			// Use bracket notation to bypass TypeScript type checking for CC API compatibility
			const updatePayload: any = { customFields: ccUpdatePayload };
			await patientReq.update(patient.ccId!, updatePayload);
			logInfo("Successfully updated CC patient custom fields", {
				requestId,
				ccId: patient.ccId,
				fieldCount: Object.keys(ccUpdatePayload).length,
			});
		} catch (error) {
			const errorMessage = `Failed to update CC patient custom fields: ${String(error)}`;
			logError(errorMessage, { requestId, ccId: patient.ccId, error });
			errors.push(errorMessage);
		}
	} else {
		logInfo("No CC custom fields to update", { requestId, ccId: patient.ccId });
	}

	return { fieldsProcessed, errors };
}

/**
 * Perform CC to AP synchronization
 */
async function performCcToApSync(
	ccCustomFields: import("@type").GetCCPatientCustomField[],
	fieldMappings: CustomFieldMapping[],
	context: SyncOperationContext,
): Promise<{ fieldsProcessed: number; errors: string[] }> {
	const { patient, requestId, skipMissingFields } = context;
	const errors: string[] = [];
	let fieldsProcessed = 0;

	// Build AP update payload
	const apUpdatePayload: APCustomFieldUpdatePayload = {
		customFields: [],
	};

	for (const ccField of ccCustomFields) {
		try {
			// Find mapping for this CC field
			const mapping = fieldMappings.find(m => m.ccId === ccField.field.id);
			if (!mapping || !mapping.apId) {
				if (skipMissingFields) {
					logWarn("Skipping CC field without AP mapping", {
						requestId,
						ccFieldId: ccField.field.id,
						reason: "No mapping found",
					});
					continue;
				} else {
					errors.push(`No AP mapping found for CC field ${ccField.field.id}`);
					continue;
				}
			}

			// Extract value from CC field structure
			const ccValue = ccField.values.length > 0
				? ccField.values.map(v => v.value).filter(v => v !== undefined).join(',')
				: null;

			// Convert value
			const conversionContext: ValueConversionContext = {
				sourceField: mapping.ccConfig!,
				targetField: mapping.apConfig!,
				sourcePlatform: "cc",
				targetPlatform: "ap",
				requestId,
			};

			const conversionResult = await convertFieldValue(ccValue, conversionContext);
			if (!conversionResult.success) {
				if (skipMissingFields) {
					logWarn("Skipping CC field due to conversion failure", {
						requestId,
						ccFieldId: ccField.field.id,
						error: conversionResult.error,
					});
					continue;
				} else {
					errors.push(`Value conversion failed for CC field ${ccField.field.id}: ${conversionResult.error}`);
					continue;
				}
			}

			// Add to AP update payload
			apUpdatePayload.customFields.push({
				id: mapping.apId,
				field_value: conversionResult.convertedValue as string | number,
			});
			fieldsProcessed++;

			logDebug("Converted CC field value for AP", {
				requestId,
				ccFieldId: ccField.field.id,
				apFieldId: mapping.apId,
				originalValue: ccValue,
				convertedValue: conversionResult.convertedValue,
			});
		} catch (error) {
			const errorMessage = `Failed to process CC field ${ccField.field.id}: ${String(error)}`;
			logError(errorMessage, { requestId, error });
			errors.push(errorMessage);
		}
	}

	// Update AP contact if we have fields to sync
	if (apUpdatePayload.customFields.length > 0) {
		try {
			await contactReq.update(patient.apId!, apUpdatePayload);
			logInfo("Successfully updated AP contact custom fields", {
				requestId,
				apId: patient.apId,
				fieldCount: apUpdatePayload.customFields.length,
			});
		} catch (error) {
			const errorMessage = `Failed to update AP contact custom fields: ${String(error)}`;
			logError(errorMessage, { requestId, apId: patient.apId, error });
			errors.push(errorMessage);
		}
	} else {
		logInfo("No AP custom fields to update", { requestId, apId: patient.apId });
	}

	return { fieldsProcessed, errors };
}
