/**
 * Custom Field Synchronization Utilities
 *
 * Utility functions for triggering custom field synchronization between
 * AutoPatient (AP) and CliniCore (CC) platforms. Provides fire-and-forget
 * functionality for webhook handlers to trigger custom field sync without
 * blocking the main webhook response.
 *
 * **Features:**
 * - Fire-and-forget pattern for non-blocking execution
 * - Internal HTTP calls to admin custom field sync endpoint
 * - Request ID correlation for tracing
 * - Graceful error handling with logging
 * - Support for both sync directions (AP ↔ CC)
 *
 * @fileoverview Custom field synchronization utilities
 * @version 1.0.0
 * @since 2024-07-28
 */

import { logInfo, logWarn } from "@/utils/logger";

/**
 * Platform type for custom field synchronization
 */
type Platform = "ap" | "cc";

/**
 * Trigger custom field synchronization for a patient
 *
 * Makes an internal HTTP call to the admin custom field sync endpoint
 * to trigger bidirectional custom field synchronization. This function
 * is designed for fire-and-forget usage in webhook handlers.
 *
 * **Implementation Details:**
 * - Uses standard fetch() API for internal HTTP calls
 * - Constructs full URL using request URL origin
 * - Passes request ID for correlation and tracing
 * - Includes skip=true parameter to handle missing fields gracefully
 * - Does not await the response (fire-and-forget pattern)
 *
 * @param patientId - Local database patient ID
 * @param platform - Target platform ("ap" or "cc")
 * @param requestId - Request ID for tracing and correlation
 * @param requestUrl - Original request URL to extract origin
 * @returns Promise that resolves when the sync request is sent
 *
 * @example
 * ```typescript
 * // Fire-and-forget custom field sync
 * triggerCustomFieldSync(patientId, 'cc', requestId, request.url)
 *   .catch(error => {
 *     logWarn('Custom field sync failed', { requestId, error: String(error) });
 *   });
 * ```
 *
 * @throws {Error} If the HTTP request fails or returns non-2xx status
 * @since 1.0.0
 */
export async function triggerCustomFieldSync(
	patientId: string,
	platform: Platform,
	requestId: string,
	requestUrl: string,
): Promise<void> {
	// Extract origin from the request URL
	const url = new URL(requestUrl);
	const baseUrl = `${url.protocol}//${url.host}`;
	
	// Construct the admin endpoint URL with skip parameter
	const syncUrl = `${baseUrl}/admin/custom-fields-sync/${patientId}/${platform}?skip=true`;

	logInfo("Triggering custom field synchronization", {
		requestId,
		patientId,
		platform,
		syncUrl,
	});

	try {
		logInfo("About to make fetch request", {
			requestId,
			patientId,
			platform,
			syncUrl,
		});

		const response = await fetch(syncUrl, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				"X-Request-ID": requestId,
			},
		});

		logInfo("Fetch request completed", {
			requestId,
			patientId,
			platform,
			status: response.status,
			statusText: response.statusText,
		});

		if (!response.ok) {
			const responseText = await response.text();
			logWarn("Custom field sync request failed", {
				requestId,
				patientId,
				platform,
				status: response.status,
				statusText: response.statusText,
				responseBody: responseText,
			});
			throw new Error(`HTTP ${response.status}: ${response.statusText} - ${responseText}`);
		}

		logInfo("Custom field sync request sent successfully", {
			requestId,
			patientId,
			platform,
			status: response.status,
		});
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		logWarn("Failed to trigger custom field sync", {
			requestId,
			patientId,
			platform,
			error: errorMessage,
		});
		throw error;
	}
}

/**
 * Trigger bidirectional custom field synchronization
 *
 * Convenience function that triggers custom field sync in both directions
 * (AP ↔ CC) for a patient. Uses fire-and-forget pattern with individual
 * error handling for each direction.
 *
 * @param patientId - Local database patient ID
 * @param requestId - Request ID for tracing and correlation
 * @param requestUrl - Original request URL to extract origin
 * @param ctx - Cloudflare Workers execution context for waitUntil
 *
 * @example
 * ```typescript
 * // Trigger bidirectional sync after successful patient sync
 * triggerBidirectionalCustomFieldSync(patientId, requestId, request.url, ctx);
 * ```
 *
 * @since 1.0.0
 */
export function triggerBidirectionalCustomFieldSync(
	patientId: string,
	requestId: string,
	requestUrl: string,
	ctx: { waitUntil: (promise: Promise<unknown>) => void },
): void {
	logInfo("Starting bidirectional custom field synchronization", {
		requestId,
		patientId,
		requestUrl,
	});

	// Trigger AP -> CC sync (fire-and-forget with proper Workers context)
	const apToCcPromise = triggerCustomFieldSync(patientId, "cc", requestId, requestUrl).catch(error => {
		logWarn("Custom field sync failed (AP->CC)", {
			requestId,
			patientId,
			error: String(error),
		});
	});

	// Trigger CC -> AP sync (fire-and-forget with proper Workers context)
	const ccToApPromise = triggerCustomFieldSync(patientId, "ap", requestId, requestUrl).catch(error => {
		logWarn("Custom field sync failed (CC->AP)", {
			requestId,
			patientId,
			error: String(error),
		});
	});

	// Use ctx.waitUntil to ensure promises complete even after response is sent
	ctx.waitUntil(apToCcPromise);
	ctx.waitUntil(ccToApPromise);

	logInfo("Bidirectional custom field sync promises queued with ctx.waitUntil", {
		requestId,
		patientId,
	});
}
