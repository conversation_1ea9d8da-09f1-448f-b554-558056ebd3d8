/**
 * CliniCore Webhook Handler
 *
 * HTTP handler for CliniCore webhook events that processes patient synchronization
 * events from CliniCore to AutoPatient (AP). This module contains only the HTTP
 * request handler function, with all business logic extracted to dedicated processor
 * modules for better maintainability and testability.
 *
 * **Features:**
 * - Clean HTTP request/response handling
 * - Request ID extraction and correlation
 * - Webhook payload validation and parsing
 * - Error handling and response formatting
 * - Integration with CliniCore webhook processor modules
 * - Comprehensive logging with request tracing
 * - Performance monitoring and statistics
 *
 * **Supported Events:**
 * - EntityWasCreated (Patient) - Creates new patient in AP and local database
 * - EntityWasUpdated (Patient) - Updates existing patient in AP and local database
 * - Future: EntityWasDeleted, Appointment events, CustomField events
 *
 * **Processing Flow:**
 * 1. Extract and validate webhook payload
 * 2. Filter events (only Patient EntityWasCreated/EntityWasUpdated)
 * 3. Look up existing patient records
 * 4. Check sync buffer to prevent loops
 * 5. Map CC patient fields to AP contact format
 * 6. Upsert contact in AutoPatient
 * 7. Update local database with sync timestamps
 * 8. Return comprehensive processing results
 *
 * @fileoverview HTTP handler for CliniCore webhook events
 * @version 1.0.0
 * @since 2024-07-27
 */

import type { Context } from "hono";
import type { CCWebhookPayload } from "@/processors/ccWebhook";
import { processWebhookEvent } from "@/processors/ccWebhook";
import { logWebhookError } from "@/utils/errorLogger";
import { logError, logInfo, logWarn } from "@/utils/logger";
import { triggerUnidirectionalCustomFieldSync } from "@/utils/customFieldSync";

/**
 * Handle CliniCore webhook events
 *
 * HTTP handler function that processes CliniCore webhook events for patient
 * synchronization. Extracts the request ID from the Hono context, validates
 * the webhook payload, delegates the processing logic to the processor module,
 * and returns a properly formatted JSON response.
 *
 * This handler is designed to be:
 * - **Lightweight**: Contains only HTTP-specific logic
 * - **Focused**: Single responsibility for request/response handling
 * - **Traceable**: Includes request ID correlation for debugging
 * - **Resilient**: Comprehensive error handling with proper HTTP status codes
 * - **Secure**: Sanitized responses that don't expose internal processing details
 * - **Fast**: Optimized for webhook timeout requirements (25 seconds)
 *
 * **Event Filtering:**
 * - Processes only `EntityWasCreated` and `EntityWasUpdated` events
 * - Processes only `Patient` model events
 * - Designed to be extensible for future event types and models
 *
 * **Sync Buffer Logic:**
 * - Compares webhook `updatedAt` with database `ccUpdatedAt`
 * - Uses configurable sync buffer time (default: 60 seconds)
 * - Prevents sync loops and unnecessary processing
 *
 * @param c - Hono context object containing request data and utilities
 * @returns Promise resolving to HTTP Response with processing results
 *
 * @example
 * ```typescript
 * // Used in Hono route definition
 * app.post('/webhooks/cc', ccWebhookHandler);
 *
 * // Response format on success (200):
 * {
 *   "requestId": "req-123",
 *   "status": "success",
 *   "message": "CliniCore webhook processed successfully",
 *   "metadata": {
 *     "timestamp": "2024-07-27T10:30:00.000Z",
 *     "durationMs": 1250,
 *     "event": "EntityWasUpdated",
 *     "model": "Patient",
 *     "entityId": 1766,
 *     "action": "updated"
 *   }
 * }
 *
 * // Response format on skipped event (200):
 * {
 *   "requestId": "req-123",
 *   "status": "success",
 *   "message": "CliniCore webhook event skipped",
 *   "reason": "Event processing was skipped due to sync buffer timing",
 *   "metadata": {
 *     "timestamp": "2024-07-27T10:30:00.000Z",
 *     "durationMs": 45,
 *     "event": "EntityWasUpdated",
 *     "model": "Patient",
 *     "entityId": 1766,
 *     "action": "skipped"
 *   }
 * }
 *
 * // Response format on error (400/500):
 * {
 *   "requestId": "req-123",
 *   "status": "error",
 *   "message": "CliniCore webhook processing failed",
 *   "metadata": {
 *     "timestamp": "2024-07-27T10:30:00.000Z",
 *     "stage": "validation"
 *   }
 * }
 * ```
 *
 * @since 1.0.0
 */
export async function ccWebhookHandler(c: Context): Promise<Response> {
	const requestId = c.get("requestId");
	const timestamp = new Date().toISOString();

	try {
		logInfo("Received CliniCore webhook request");

		// Parse and validate webhook payload
		const payload = await parseWebhookPayload(c);
		if (!payload) {
			return c.json(
				{
					requestId,
					status: "error",
					message: "CliniCore webhook processing failed",
					metadata: {
						timestamp,
						stage: "validation",
					},
				},
				400,
			);
		}

		logInfo(
			`Processing CC webhook: ${payload.event} ${payload.model} ID:${payload.id}`,
		);

		// Process the webhook event
		const result = await processWebhookEvent(payload, requestId);

		// Handle successful processing
		if (result.success) {
			// Trigger custom field synchronization for created/updated patients
			if (
				result.patientSync?.patient &&
				["created", "updated"].includes(result.patientSync.action)
			) {
				const patientId = result.patientSync.patient.id;
				logInfo("Triggering custom field sync after successful patient processing", {
					requestId,
					patientId,
					action: result.patientSync.action,
				});

				// Fire-and-forget unidirectional custom field sync (CC → AP)
				// CliniCore webhook means CC data changed, so sync to AutoPatient
				triggerUnidirectionalCustomFieldSync(patientId, "ap", requestId, c.req.url, c.executionCtx);
			}

			const responseMessage =
				result.patientSync?.action === "skipped"
					? "CliniCore webhook event skipped"
					: "CliniCore webhook processed successfully";

			// Create sanitized response with only public fields
			const response: Record<string, unknown> = {
				requestId,
				status: "success",
				message: responseMessage,
				metadata: {
					timestamp,
					durationMs: result.metadata.durationMs,
					event: result.event,
					model: result.model,
					entityId: result.entityId,
					action: result.patientSync?.action || "processed",
				},
			};

			// Add skip reason if applicable (without exposing internal details)
			if (result.patientSync?.action === "skipped") {
				response.reason =
					"Event processing was skipped due to sync buffer timing";
			}

			logInfo(
				`CC webhook processing completed: ${result.event} ${result.model} ` +
					`(${result.metadata.durationMs}ms)`,
			);

			return c.json(response, 200);
		}

		// Handle processing errors
		await logWebhookError(
			new Error(result.error?.message || "Unknown processing error"),
			payload as unknown as Record<string, unknown>,
		);

		logError(
			`CC webhook processing failed: ${result.error?.message} ` +
				`(stage: ${result.error?.stage})`,
		);

		// Create sanitized error response without exposing internal details
		return c.json(
			{
				requestId,
				status: "error",
				message: "CliniCore webhook processing failed",
				metadata: {
					timestamp,
					event: result.event,
					model: result.model,
					entityId: result.entityId,
				},
			},
			500,
		);
	} catch (error) {
		// Handle unexpected errors
		const errorMessage = error instanceof Error ? error.message : String(error);

		await logWebhookError(
			error instanceof Error ? error : new Error(errorMessage),
			{ error: "Unexpected handler error" },
		);

		logError(
			`Unexpected error in CC webhook handler: ${errorMessage}`,
		);

		return c.json(
			{
				error: "CliniCore webhook processing failed",
				details: "Unexpected server error",
				requestId,
				timestamp,
				stage: "handler",
			},
			500,
		);
	}
}

/**
 * Parse and validate webhook payload from request
 *
 * Extracts the JSON payload from the request body and validates its structure
 * to ensure it contains the required fields for webhook processing.
 *
 * @param c - Hono context object
 * @param requestId - Request ID for logging
 * @returns Parsed webhook payload or null if invalid
 */
async function parseWebhookPayload(
	c: Context,
): Promise<CCWebhookPayload | null> {
	try {
		const body = await c.req.json();

		// Validate required fields
		if (!body || typeof body !== "object") {
			logWarn("Webhook payload is not a valid object");
			return null;
		}

		const { event, model, id, payload, timestamp } = body;

		// Check for required fields
		if (!event || !model || id === undefined || !payload) {
			logWarn(
				`Webhook payload missing required fields: ` +
					`event=${!!event}, model=${!!model}, id=${id !== undefined}, payload=${!!payload}`,
			);
			return null;
		}

		// Validate field types
		if (
			typeof event !== "string" ||
			typeof model !== "string" ||
			typeof id !== "number"
		) {
			logWarn(
				`Webhook payload has invalid field types: ` +
					`event=${typeof event}, model=${typeof model}, id=${typeof id}`,
			);
			return null;
		}

		logInfo(
			`Successfully parsed webhook payload: ${event} ${model} ID:${id}`,
		);

		return {
			event: event as CCWebhookPayload["event"],
			model: model as CCWebhookPayload["model"],
			id,
			payload,
			timestamp: timestamp || new Date().toISOString(),
		};
	} catch (error) {
		logError(`Failed to parse webhook payload: ${error}`);
		return null;
	}
}
