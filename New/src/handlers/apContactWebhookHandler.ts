/**
 * AutoPatient Contact Webhook Handler
 *
 * HTTP handler for AutoPatient contact webhook events that processes contact creation
 * events from AutoPatient to CliniCore (CC). This module contains only the HTTP
 * request handler function, with all business logic extracted to dedicated processor
 * modules for better maintainability and testability.
 *
 * **Features:**
 * - Clean HTTP request/response handling
 * - Request ID extraction and correlation
 * - Webhook payload validation and parsing
 * - Error handling and response formatting
 * - Integration with AutoPatient webhook processor modules
 * - Comprehensive logging with request tracing
 * - Performance monitoring and statistics
 *
 * **Supported Events:**
 * - contact_created - Creates new patient in CC and local database
 * - Future: contact_updated, contact_deleted
 *
 * **Processing Flow:**
 * 1. Extract and validate webhook payload
 * 2. Filter events (only contact_created for now)
 * 3. Look up existing patient records
 * 4. Check sync buffer to prevent loops
 * 5. Map AP contact fields to CC patient format
 * 6. Create/update patient in CliniCore
 * 7. Update local database with sync timestamps
 * 8. Return comprehensive processing results
 *
 * @fileoverview HTTP handler for AutoPatient contact webhook events
 * @version 1.0.0
 * @since 2024-07-27
 */

import type { Context } from "hono";
import type { APContactCreationWebhookPayload } from "@/processors/apWebhook/types";
import { processContactWebhookEvent } from "@/processors/apWebhook/eventProcessor";
import { logWebhookError } from "@/utils/errorLogger";
import { logError, logInfo, logWarn } from "@/utils/logger";
import { triggerBidirectionalCustomFieldSync } from "@/utils/customFieldSync";

/**
 * Handle AutoPatient contact webhook events
 *
 * HTTP handler function that processes AutoPatient contact webhook events for patient
 * synchronization. Extracts the request ID from the Hono context, validates
 * the webhook payload, delegates the processing logic to the processor module,
 * and returns a properly formatted JSON response.
 *
 * This handler is designed to be:
 * - **Lightweight**: Contains only HTTP-specific logic
 * - **Focused**: Single responsibility for request/response handling
 * - **Traceable**: Includes request ID correlation for debugging
 * - **Resilient**: Comprehensive error handling with proper HTTP status codes
 * - **Secure**: Sanitized responses that don't expose internal processing details
 * - **Fast**: Optimized for webhook timeout requirements (25 seconds)
 *
 * **Event Filtering:**
 * - Processes only `contact_created` events initially
 * - Designed to be extensible for future event types
 *
 * **Sync Buffer Logic:**
 * - Compares webhook `date_created` with database `apUpdatedAt`
 * - Uses configurable sync buffer time (default: 60 seconds)
 * - Prevents sync loops and unnecessary processing
 *
 * @param c - Hono context object containing request data and utilities
 * @returns Promise resolving to HTTP Response with processing results
 *
 * @example
 * ```typescript
 * // Used in Hono route definition
 * app.post('/webhooks/ap/contact', apContactWebhookHandler);
 *
 * // Response format on success (200):
 * {
 *   "requestId": "req-abc123",
 *   "status": "success",
 *   "message": "AutoPatient contact webhook processed successfully",
 *   "metadata": {
 *     "timestamp": "2024-07-27T10:30:00.000Z",
 *     "durationMs": 1250,
 *     "event": "contact_created",
 *     "contactId": "k1iaEbGJt6MJWXgHGRrt",
 *     "action": "created"
 *   }
 * }
 *
 * // Response format on error (400/500):
 * {
 *   "requestId": "req-abc123",
 *   "status": "error",
 *   "message": "AutoPatient contact webhook processing failed",
 *   "metadata": {
 *     "timestamp": "2024-07-27T10:30:00.000Z",
 *     "stage": "validation"
 *   }
 * }
 * ```
 */
export async function apContactWebhookHandler(c: Context): Promise<Response> {
	const requestId = c.get("requestId") || `ap-${crypto.randomUUID()}`;
	const timestamp = new Date().toISOString();

	try {
		logInfo("Received AutoPatient contact webhook request");

		// Parse and validate webhook payload
		const payload = await parseWebhookPayload(c);
		if (!payload) {
			return c.json(
				{
					requestId,
					status: "error",
					message: "AutoPatient contact webhook processing failed",
					metadata: {
						timestamp,
						stage: "validation",
					},
				},
				400,
			);
		}

		logInfo(
			`Processing AP contact webhook: contact_created ID:${payload.contact_id}`,
		);

		// Process the webhook event
		const result = await processContactWebhookEvent(payload, requestId);

		// Handle successful processing
		if (result.success) {
			// Trigger custom field synchronization for created/updated contacts
			if (
				result.contactSync?.dbPatient &&
				["created", "updated"].includes(result.contactSync.action)
			) {
				const patientId = result.contactSync.dbPatient.id;
				logInfo("Triggering custom field sync after successful contact processing", {
					requestId,
					patientId,
					action: result.contactSync.action,
				});

				// Fire-and-forget bidirectional custom field sync
				triggerBidirectionalCustomFieldSync(patientId, requestId, c.req.url);
			}

			const responseMessage =
				result.contactSync?.action === "skipped"
					? "AutoPatient contact webhook event skipped"
					: "AutoPatient contact webhook processed successfully";

			// Create sanitized response with only public fields
			const response: Record<string, unknown> = {
				requestId,
				status: "success",
				message: responseMessage,
				metadata: {
					timestamp,
					durationMs: result.metadata.durationMs,
					event: result.event,
					contactId: result.contactId,
					action: result.contactSync?.action || "processed",
				},
			};

			// Add skip reason if applicable (without exposing internal details)
			if (result.contactSync?.action === "skipped") {
				response.reason =
					"Event processing was skipped due to sync buffer timing";
			}

			logInfo(
				`AP contact webhook processing completed: ${result.event} ` +
					`contact:${result.contactId} (${result.metadata.durationMs}ms)`,
			);

			return c.json(response, 200);
		}

		// Handle processing errors
		await logWebhookError(
			new Error(result.error?.message || "Unknown processing error"),
			payload as unknown as Record<string, unknown>,
		);

		logError(
			`AP contact webhook processing failed: ${result.error?.message} ` +
				`(stage: ${result.error?.stage})`,
		);

		// Create sanitized error response without exposing internal details
		return c.json(
			{
				requestId,
				status: "error",
				message: "AutoPatient contact webhook processing failed",
				metadata: {
					timestamp,
					event: result.event,
					contactId: result.contactId,
				},
			},
			500,
		);
	} catch (error) {
		// Handle unexpected errors
		await logWebhookError(
			error instanceof Error ? error : new Error(String(error)),
		);

		logError(
			"Unexpected error in AP contact webhook handler",
			error,
		);

		return c.json(
			{
				requestId,
				status: "error",
				message: "AutoPatient contact webhook processing failed",
				metadata: {
					timestamp,
					stage: "handler",
				},
			},
			500,
		);
	}
}

/**
 * Parse and validate AutoPatient contact webhook payload
 *
 * Extracts the JSON payload from the request body and validates that it contains
 * the required fields for contact webhook processing. Performs type checking
 * and structural validation to ensure the payload matches expected format.
 *
 * @param c - Hono context object
 * @param requestId - Request ID for logging correlation
 * @returns Parsed and validated webhook payload, or null if invalid
 */
async function parseWebhookPayload(
	c: Context,
): Promise<APContactCreationWebhookPayload | null> {
	try {
		const body = await c.req.json();

		logInfo(
			"Parsing AP contact webhook payload",
			{ hasBody: !!body },
		);

		if (!body || typeof body !== "object") {
			logWarn("Webhook payload is not a valid object");
			return null;
		}

		const { contact_id, date_created, workflow, calendar } = body;

		// Check for calendar field - skip if present (appointment-related event)
		if (calendar !== undefined) {
			logInfo(
				"Skipping webhook - calendar field present (appointment-related event)",
			);
			return null;
		}

		// Check for required fields
		if (!contact_id || !date_created || !workflow) {
			logWarn(
				`Webhook payload missing required fields: ` +
					`contact_id=${!!contact_id}, date_created=${!!date_created}, workflow=${!!workflow}`,
			);
			return null;
		}

		// Validate field types
		if (
			typeof contact_id !== "string" ||
			typeof date_created !== "string" ||
			typeof workflow !== "object"
		) {
			logWarn(
				`Webhook payload has invalid field types: ` +
					`contact_id=${typeof contact_id}, date_created=${typeof date_created}, workflow=${typeof workflow}`,
			);
			return null;
		}

		logInfo(
			`Successfully parsed webhook payload: contact_created ID:${contact_id}`,
		);

		return body as APContactCreationWebhookPayload;
	} catch (error) {
		logError(
			"Failed to parse webhook payload",
			error,
		);
		return null;
	}
}
